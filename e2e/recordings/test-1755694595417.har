{"log": {"version": "1.2", "creator": {"name": "Playwright", "version": "1.54.2"}, "browser": {"name": "electron", "version": "138.0.7204.235"}, "entries": [{"startedDateTime": "2025-08-20T12:56:35.825Z", "time": 3.905, "request": {"method": "GET", "url": "app://obsidian.md/index.html", "httpVersion": "app", "cookies": [], "headers": [], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Content-Type", "value": "text/html"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/html"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 3.905}}, {"startedDateTime": "2025-08-20T12:56:35.919Z", "time": 8.125, "request": {"method": "GET", "url": "app://obsidian.md/app.css", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/css"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/css"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.125}}, {"startedDateTime": "2025-08-20T12:56:35.920Z", "time": 10.987, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/codemirror.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 10.987}}, {"startedDateTime": "2025-08-20T12:56:35.920Z", "time": 4.873, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/overlay.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.873}}, {"startedDateTime": "2025-08-20T12:56:35.920Z", "time": 5.073, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/markdown.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.073}}, {"startedDateTime": "2025-08-20T12:56:35.920Z", "time": 4.881, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/cm-addons.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.881}}, {"startedDateTime": "2025-08-20T12:56:35.920Z", "time": 6.742, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/vim.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 6.742}}, {"startedDateTime": "2025-08-20T12:56:35.920Z", "time": 5.255, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/meta.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.255}}, {"startedDateTime": "2025-08-20T12:56:35.920Z", "time": 15.99, "request": {"method": "GET", "url": "app://obsidian.md/lib/moment.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 15.99}}, {"startedDateTime": "2025-08-20T12:56:35.920Z", "time": 20.785, "request": {"method": "GET", "url": "app://obsidian.md/lib/pixi.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 20.785}}, {"startedDateTime": "2025-08-20T12:56:35.921Z", "time": 5.225, "request": {"method": "GET", "url": "app://obsidian.md/lib/i18next.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.225}}, {"startedDateTime": "2025-08-20T12:56:35.921Z", "time": 5.389, "request": {"method": "GET", "url": "app://obsidian.md/lib/scrypt.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.389}}, {"startedDateTime": "2025-08-20T12:56:35.921Z", "time": 5.239, "request": {"method": "GET", "url": "app://obsidian.md/lib/turndown.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.239}}, {"startedDateTime": "2025-08-20T12:56:35.921Z", "time": 5.465, "request": {"method": "GET", "url": "app://obsidian.md/enhance.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.465}}, {"startedDateTime": "2025-08-20T12:56:35.921Z", "time": 6.049, "request": {"method": "GET", "url": "app://obsidian.md/i18n.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 6.049}}, {"startedDateTime": "2025-08-20T12:56:35.921Z", "time": 64.847, "request": {"method": "GET", "url": "app://obsidian.md/app.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 64.847}}, {"startedDateTime": "2025-08-20T12:56:36.017Z", "time": 32.128, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/535a6cf662596b3bd6a6.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 32.128}}, {"startedDateTime": "2025-08-20T12:56:36.017Z", "time": 21.671, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/cb10ffd7684cd9836a05.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 21.671}}, {"startedDateTime": "2025-08-20T12:56:36.017Z", "time": 26.045, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/cbe0ae49c52c920fd563.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 26.045}}, {"startedDateTime": "2025-08-20T12:56:36.017Z", "time": 31.846, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/b5f0f109bc88052d4000.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 31.846}}, {"startedDateTime": "2025-08-20T12:56:36.017Z", "time": 34.487, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/c8ba52b05a9ef10f4758.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 34.487}}, {"startedDateTime": "2025-08-20T12:56:36.017Z", "time": 37.101, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/2d5198822ab091ce4305.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 37.101}}, {"startedDateTime": "2025-08-20T12:56:36.017Z", "time": 39.821, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/72505e6a122c6acd5471.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 39.821}}, {"startedDateTime": "2025-08-20T12:56:36.229Z", "time": 656.464, "request": {"method": "GET", "url": "app://obsidian.md/worker.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 656.464}}, {"startedDateTime": "2025-08-20T12:56:36.235Z", "time": 640.716, "request": {"method": "GET", "url": "blob:app://obsidian.md/f89c8439-9fd7-4dbf-9b60-781b49121635", "httpVersion": "blob", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "blob", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Content-Length", "value": "4587"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 640.716}}]}}