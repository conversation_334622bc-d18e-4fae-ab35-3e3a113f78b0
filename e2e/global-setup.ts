import * as fs from 'fs';
import * as path from 'path';

/**
 * Global setup for all e2e tests
 * Prepares test environment without creating shared Electron instances
 * Each worker will create its own isolated Electron instance
 */
export default async function globalSetup() {
  console.log("🌍 Starting global e2e test setup...");

  try {
    // Ensure test environments directory exists
    const testEnvDir = path.join(process.cwd(), 'e2e/test-environments');
    await fs.promises.mkdir(testEnvDir, { recursive: true });

    // Clean up any leftover test environments from previous runs
    if (fs.existsSync(testEnvDir)) {
      const entries = await fs.promises.readdir(testEnvDir);
      for (const entry of entries) {
        const entryPath = path.join(testEnvDir, entry);
        try {
          await fs.promises.rm(entryPath, { recursive: true, force: true });
        } catch (error) {
          console.log(`⚠️ Could not clean up ${entry}:`, error.message);
        }
      }
    }

    console.log("✅ Global e2e test setup complete");

  } catch (error) {
    console.error("❌ Global setup failed:", error);
    throw error;
  }
}
